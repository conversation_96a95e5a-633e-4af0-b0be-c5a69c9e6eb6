import request from '@/utils/request'

// 查询3D模型场景配置文件列表
export function listScene(query) {
  return request({
    url: '/holo-api/Holo3dModelScene/scene/v1/list',
    method: 'get',
    params: query
  })
}

// 查询3D模型场景配置文件详细
export function getScene(id) {
  return request({
    url: '/holo-api/Holo3dModelScene/scene/v1/' + id,
    method: 'get'
  })
}

// 新增3D模型场景配置文件
export function addScene(data) {
  return request({
    url: '/holo-api/Holo3dModelScene/scene/v1',
    method: 'post',
    data: data
  })
}

// 修改3D模型场景配置文件
export function updateScene(data) {
  return request({
    url: '/holo-api/Holo3dModelScene/scene/v1',
    method: 'put',
    data: data
  })
}

// 删除3D模型场景配置文件
export function delScene(id) {
  return request({
    url: '/holo-api/Holo3dModelScene/scene/v1/' + id,
    method: 'delete'
  })
}


// 查询3D模型场景配置文件详细
export function getSceneByCode(code) {
  return request({
    url: '/holo-api/Holo3dModelScene/scene/v1/' + code,
    method: 'get'
  })
}
