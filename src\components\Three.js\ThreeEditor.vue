<template>
  <div class="three-editor-container">
    <div ref="editorContainer" class="editor-wrapper" :style="containerStyle">
      <!-- 直接使用原始Three.js编辑器 -->
      <iframe
        ref="editorFrame"
        :src="editorUrl"
        class="editor-iframe"
        @load="onEditorLoad"
        frameborder="0"
        allowfullscreen
      ></iframe>

      <!-- 加载状态覆盖层 -->
      <div v-if="!isReady" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <p>正在加载Three.js编辑器...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

// Props定义
const props = defineProps({
  width: {
    type: [String, Number],
    default: '100%'
  },
  height: {
    type: [String, Number],
    default: '600px'
  },
  visible: {
    type: Boolean,
    default: true
  },
  autoSave: {
    type: Boolean,
    default: true
  },
  initialScene: {
    type: Object,
    default: null
  },
  useSimpleEditor: {
    type: Boolean,
    default: false
  },
  modelLibraryData: {
    type: Object,
    default: () => ({
      systemLibrary: [],
      userLibrary: []
    })
  },
  spotlightData: {
    type: Array,
    default: () => []
  }
})

// Emits定义
const emit = defineEmits([
  'ready',
  'sceneChanged',
  'objectSelected',
  'objectAdded',
  'objectRemoved',
  'save',
  'load'
])

// 响应式数据
const editorContainer = ref(null)
const editorFrame = ref(null)
const isReady = ref(false)

// 编辑器实例
let editor = null

// 计算属性
const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  display: props.visible ? 'block' : 'none'
}))

const editorUrl = computed(() => {
  // 根据props选择编辑器版本
  if (props.useSimpleEditor) {
    return '/three-editor-simple.html'
  }
  // 使用完整版编辑器
  return '/three-editor/editor/index.html'
})

// iframe加载完成处理
const onEditorLoad = () => {
  console.log('iframe加载完成，正在初始化编辑器...')

  // 使用多次重试机制
  let retryCount = 0
  const maxRetries = 5

  const tryInitEditor = () => {
    try {
      const iframe = editorFrame.value
      if (iframe && iframe.contentWindow) {
        // 获取iframe中的编辑器实例
        editor = iframe.contentWindow.editor

        if (editor) {
          setupEditorEvents()

          // 注入SVG符号到iframe中
          injectSvgSymbols(iframe)

          isReady.value = true
          emit('ready', editor)

          // 延迟传递模型库数据到编辑器，确保ResourcePanel已初始化
          setTimeout(() => {
            if (props.modelLibraryData && iframe.contentWindow.setModelLibraryData) {
              iframe.contentWindow.setModelLibraryData(props.modelLibraryData)
              console.log('模型库数据已传递到编辑器:', props.modelLibraryData)
            }

            // 传递亮点数据到编辑器
            if (props.spotlightData && props.spotlightData.length > 0 && iframe.contentWindow.setSpotlightData) {
              iframe.contentWindow.setSpotlightData(props.spotlightData)
              console.log('✅ 亮点数据已传递到编辑器:', props.spotlightData)
            }
          }, 1000)

          // 如果有初始场景，加载它
          if (props.initialScene) {
            loadScene(props.initialScene)
          }

          console.log('✅ Three.js编辑器已成功加载')
          return true
        } else {
          console.log(`⏳ 编辑器实例尚未准备就绪 (尝试 ${retryCount + 1}/${maxRetries})`)
          return false
        }
      }
    } catch (error) {
      console.warn('访问编辑器时出现错误:', error.message)
      return false
    }
    return false
  }

  // 立即尝试一次
  if (tryInitEditor()) {
    return
  }

  // 如果失败，使用递增延迟重试
  const retryWithDelay = () => {
    retryCount++
    if (retryCount >= maxRetries) {
      console.warn('⚠️ 达到最大重试次数，编辑器可能无法完全初始化')
      console.log('💡 编辑器界面仍然可用，但API功能可能受限')
      isReady.value = true
      emit('ready', null)
      return
    }

    const delay = Math.min(1000 * retryCount, 5000) // 递增延迟，最大5秒
    setTimeout(() => {
      if (!tryInitEditor()) {
        retryWithDelay()
      }
    }, delay)
  }

  retryWithDelay()
}

// 注入SVG符号到iframe中
const injectSvgSymbols = (iframe) => {
  try {
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document

    // 查找所有可能的SVG符号容器
    const svgContainers = [
      document.querySelector('svg[style*="display: none"]'),
      document.querySelector('svg[style*="display:none"]'),
      document.querySelector('#__svg__icons__dom__'),
      document.querySelector('svg[data-svg-icons]'),
      document.body.querySelector('svg:first-child')
    ]

    let symbolsFound = false

    for (const container of svgContainers) {
      if (container && container.innerHTML.includes('<symbol')) {
        // 克隆SVG符号并注入到iframe中
        const clonedSymbols = container.cloneNode(true)
        clonedSymbols.style.display = 'none'
        clonedSymbols.style.position = 'absolute'

        // 将SVG符号添加到iframe的body开头
        if (iframeDoc.body) {
          iframeDoc.body.insertBefore(clonedSymbols, iframeDoc.body.firstChild)
          console.log('✅ SVG符号已注入到编辑器iframe中', container)

          // 调试：检查注入的符号数量
          const symbols = clonedSymbols.querySelectorAll('symbol')
          console.log(`📊 注入了 ${symbols.length} 个SVG符号:`, Array.from(symbols).map(s => s.id))

          symbolsFound = true
          break
        }
      }
    }

    if (!symbolsFound) {
      console.warn('⚠️ 未找到父页面中的SVG符号容器')
      // 打印所有SVG元素用于调试
      const allSvgs = document.querySelectorAll('svg')
      console.log('父页面中的所有SVG元素:', allSvgs)
    }
  } catch (error) {
    console.error('❌ 注入SVG符号失败:', error)
  }
}

// 设置编辑器事件监听
const setupEditorEvents = () => {
  if (!editor || !editor.signals) return

  try {
    const signals = editor.signals

    // 监听场景变化
    if (signals.sceneGraphChanged) {
      signals.sceneGraphChanged.add(() => {
        emit('sceneChanged', getSceneData())
      })
    }

    // 监听对象选择
    if (signals.objectSelected) {
      signals.objectSelected.add((object) => {
        emit('objectSelected', object)
      })
    }

    // 监听对象添加
    if (signals.objectAdded) {
      signals.objectAdded.add((object) => {
        emit('objectAdded', object)
      })
    }

    // 监听对象移除
    if (signals.objectRemoved) {
      signals.objectRemoved.add((object) => {
        emit('objectRemoved', object)
      })
    }

    // 监听保存事件
    if (signals.savingFinished) {
      signals.savingFinished.add(() => {
        emit('save', getSceneData())
      })
    }

    // 监听自定义保存事件（用于导出完整场景和GLB）
    // 创建一个全局的保存事件监听器，通过window对象通信
    window.addEventListener('customSaveEvent', (event) => {
      console.log('🎯 ThreeEditor组件收到完整场景导出事件:', event.detail)
      console.log('📤 准备通过emit传递给父组件...')

      const saveData = {
        type: 'export',
        exportData: event.detail.exportData,
        glbData: event.detail.glbData,
        timestamp: event.detail.timestamp,
        stats: event.detail.stats
      };

      console.log('📦 传递给父组件的数据:', {
        type: saveData.type,
        hasExportData: !!saveData.exportData,
        hasGlbData: !!saveData.glbData,
        timestamp: saveData.timestamp,
        stats: saveData.stats
      });

      emit('save', saveData)
      console.log('✅ 数据已通过emit传递给父组件')
    })

    // 同时监听postMessage事件（备用方案）
    window.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'customSaveEvent') {
        console.log('📨 通过postMessage收到完整场景导出事件:', event.data.detail)
        console.log('📤 准备通过emit传递给父组件...')

        const saveData = {
          type: 'export',
          exportData: event.data.detail.exportData,
          glbData: event.data.detail.glbData,
          timestamp: event.data.detail.timestamp,
          stats: event.data.detail.stats
        };

        console.log('📦 传递给父组件的数据:', {
          type: saveData.type,
          hasExportData: !!saveData.exportData,
          hasGlbData: !!saveData.glbData,
          timestamp: saveData.timestamp,
          stats: saveData.stats
        });

        emit('save', saveData)
        console.log('✅ 数据已通过emit传递给父组件（来自postMessage）')
      }
    })

    console.log('✅ 完整场景导出事件监听已设置（window事件 + postMessage）')

    console.log('编辑器事件监听已设置')
  } catch (error) {
    console.error('设置编辑器事件监听失败:', error)
  }
}

// 公共API方法
const getSceneData = () => {
  if (!editor) return null
  try {
    return editor.toJSON()
  } catch (error) {
    console.error('获取场景数据失败:', error)
    return null
  }
}

const loadScene = (sceneData) => {
  if (!editor || !sceneData) return

  try {
    editor.clear()

    // 尝试转换场景数据格式
    const processedSceneData = processSceneData(sceneData)

    editor.fromJSON(processedSceneData)
    emit('load', processedSceneData)
  } catch (error) {
    console.error('加载场景失败:', error)
    console.error('场景数据:', sceneData)
    throw error
  }
}

// 处理和转换场景数据格式
const processSceneData = (sceneData) => {
  // 如果数据已经是正确格式，直接返回
  if (sceneData.metadata && sceneData.scene && sceneData.scene.object && sceneData.scene.object.type) {
    return sceneData
  }

  // 生成UUID的简单函数
  const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  // 尝试构建标准的Three.js编辑器场景格式
  const processedData = {
    metadata: sceneData.metadata || {
      version: 4.5,
      type: 'Object',
      generator: 'Object3D.toJSON'
    },
    scene: {
      metadata: {
        version: 4.5,
        type: 'Object',
        generator: 'Object3D.toJSON'
      },
      object: sceneData.scene?.object || sceneData.object || {
        uuid: generateUUID(),
        type: 'Scene',
        name: 'Scene',
        layers: 1,
        matrix: [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1],
        children: []
      }
    },
    camera: sceneData.camera || {
      metadata: {
        version: 4.5,
        type: 'Object',
        generator: 'Object3D.toJSON'
      },
      object: {
        uuid: generateUUID(),
        type: 'PerspectiveCamera',
        name: 'Camera',
        layers: 1,
        matrix: [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1],
        fov: 50,
        zoom: 1,
        near: 0.1,
        far: 2000,
        focus: 10,
        aspect: 1.7777777777777777,
        filmGauge: 35,
        filmOffset: 0
      }
    },
    project: sceneData.project || {
      shadows: true,
      shadowType: 1,
      toneMapping: 0,
      toneMappingExposure: 1
    },
    scripts: sceneData.scripts || {},
    history: sceneData.history || {
      undos: [],
      redos: []
    },
    environment: sceneData.environment || null
  }

  console.log('处理后的场景数据:', processedData)
  return processedData
}

// 通过URL加载场景JSON文件
const loadSceneFromUrl = async (sceneJsonUrl) => {
  if (!editor || !sceneJsonUrl) return

  try {
    console.log('正在从URL加载场景:', sceneJsonUrl)

    // 获取场景JSON文件
    const response = await fetch(sceneJsonUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const sceneText = await response.text()
    console.log('原始场景文本:', sceneText)

    const sceneData = JSON.parse(sceneText)
    console.log('解析后的场景数据:', sceneData)

    // 验证场景数据格式
    if (!validateSceneData(sceneData)) {
      throw new Error('场景数据格式不正确')
    }

    // 使用loadScene方法加载场景
    loadScene(sceneData)

    return sceneData
  } catch (error) {
    console.error('从URL加载场景失败:', error)
    console.error('错误详情:', error.message)
    throw error
  }
}

// 验证场景数据格式
const validateSceneData = (sceneData) => {
  if (!sceneData || typeof sceneData !== 'object') {
    console.error('场景数据不是有效的对象')
    return false
  }

  // 检查是否有必要的字段
  if (!sceneData.metadata) {
    console.error('场景数据缺少 metadata 字段')
    return false
  }

  // 检查场景对象
  if (!sceneData.scene && !sceneData.object) {
    console.error('场景数据缺少 scene 或 object 字段')
    return false
  }

  // 如果有scene字段，检查其结构
  if (sceneData.scene) {
    if (!sceneData.scene.object || !sceneData.scene.object.type) {
      console.error('场景数据的 scene.object 缺少 type 字段')
      return false
    }
  }

  // 如果有object字段，检查其结构
  if (sceneData.object && !sceneData.object.type) {
    console.error('场景数据的 object 缺少 type 字段')
    return false
  }

  console.log('场景数据格式验证通过')
  return true
}

const addObject = (object) => {
  if (!editor) return
  try {
    editor.addObject(object)
  } catch (error) {
    console.error('添加对象失败:', error)
  }
}

const removeObject = (object) => {
  if (!editor) return
  try {
    editor.removeObject(object)
  } catch (error) {
    console.error('移除对象失败:', error)
  }
}

const selectObject = (object) => {
  if (!editor) return
  try {
    editor.select(object)
  } catch (error) {
    console.error('选择对象失败:', error)
  }
}

const clearScene = () => {
  if (!editor) return
  try {
    editor.clear()
  } catch (error) {
    console.error('清空场景失败:', error)
  }
}

const exportScene = (format = 'json') => {
  if (!editor) return null

  try {
    switch (format) {
      case 'json':
        return editor.toJSON()
      case 'gltf':
        console.warn('GLTF导出功能需要在原始编辑器中实现')
        return null
      default:
        return editor.toJSON()
    }
  } catch (error) {
    console.error('导出场景失败:', error)
    return null
  }
}

const importFile = (file) => {
  if (!editor || !file) return
  try {
    if (editor.loader && editor.loader.loadFile) {
      editor.loader.loadFile(file)
    } else {
      console.warn('编辑器不支持文件导入功能')
    }
  } catch (error) {
    console.error('导入文件失败:', error)
  }
}

const executeCommand = (commandName, ...args) => {
  if (!editor) return

  try {
    if (editor[commandName] && typeof editor[commandName] === 'function') {
      return editor[commandName](...args)
    } else {
      console.warn(`编辑器不支持命令: ${commandName}`)
    }
  } catch (error) {
    console.error(`执行命令 ${commandName} 失败:`, error)
  }
}

const getEditorState = () => {
  if (!editor) return null

  try {
    return {
      scene: editor.scene,
      camera: editor.camera,
      renderer: editor.renderer,
      selected: editor.selected,
      isReady: isReady.value
    }
  } catch (error) {
    console.error('获取编辑器状态失败:', error)
    return null
  }
}

// 监听props变化
watch(() => props.visible, (visible) => {
  if (visible && !isReady.value) {
    // iframe会自动加载，无需手动初始化
  }
})

watch(() => props.initialScene, (newScene) => {
  if (newScene && isReady.value) {
    loadScene(newScene)
  }
})

// 监听模型库数据变化
watch(() => props.modelLibraryData, (newData) => {
  if (newData && isReady.value && editorFrame.value) {
    const iframe = editorFrame.value
    if (iframe.contentWindow && iframe.contentWindow.setModelLibraryData) {
      iframe.contentWindow.setModelLibraryData(newData)
      console.log('模型库数据已更新到编辑器:', newData)
    }
  }
}, { deep: true })

// 监听亮点数据变化
watch(() => props.spotlightData, (newData) => {
  console.log('ThreeEditor组件收到亮点数据:', newData)
  if (newData && newData.length > 0) {
    // 如果编辑器还没准备好，等待一下再传递
    if (!isReady.value) {
      console.log('编辑器还没准备好，等待传递亮点数据...')
      const checkReady = () => {
        if (isReady.value && editorFrame.value) {
          const iframe = editorFrame.value
          if (iframe.contentWindow && iframe.contentWindow.setSpotlightData) {
            iframe.contentWindow.setSpotlightData(newData)
            console.log('亮点数据已延迟更新到编辑器iframe:', newData)
          }
        } else {
          setTimeout(checkReady, 100) // 100ms后再检查
        }
      }
      setTimeout(checkReady, 100)
    } else {
      // 编辑器已准备好，直接传递
      const iframe = editorFrame.value
      if (iframe && iframe.contentWindow && iframe.contentWindow.setSpotlightData) {
        iframe.contentWindow.setSpotlightData(newData)
        console.log('亮点数据已更新到编辑器iframe:', newData)
      } else {
        console.log('iframe.contentWindow.setSpotlightData 不存在')
      }
    }
  } else {
    console.log('亮点数据传递条件不满足:', { newData: !!newData, isReady: isReady.value, editorFrame: !!editorFrame.value })
  }
}, { deep: true })

onMounted(() => {
  // iframe会自动加载编辑器
  console.log('ThreeEditor组件已挂载')
})

onUnmounted(() => {
  // 清理工作
  editor = null
  console.log('ThreeEditor组件已卸载')
})

// 暴露方法给父组件
defineExpose({
  getSceneData,
  loadScene,
  loadSceneFromUrl,
  addObject,
  removeObject,
  selectObject,
  clearScene,
  exportScene,
  importFile,
  executeCommand,
  getEditorState,
  isReady: () => isReady.value,
  getEditor: () => editor
})
</script>

<style scoped>
.three-editor-container {
  position: relative;
  width: 100%;
  height: 100%;
  font-family: 'Lucida Grande', sans-serif;
  font-size: 12px;
  background: #ddd;
}

.editor-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #ddd;
}

.editor-iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>